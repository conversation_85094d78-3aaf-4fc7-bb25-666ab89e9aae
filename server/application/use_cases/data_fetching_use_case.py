"""
Data Fetching Use Case

This module provides a focused use case for retrieving and storing stock data 
from external providers. It handles only data acquisition and persistence,
without any analysis logic.
"""

import logging
from typing import List, Optional
from datetime import datetime

from ...shared.models.stock_models import PricePoint, DataProvider
from ...shared.exceptions.stock_exceptions import (
    DataFetchException, SymbolNotFoundException, NetworkException
)
from ...shared.utils.validation import validate_symbol, validate_provider, validate_days_back
from ...core.services.data_service import DataService


class DataFetchingUseCase:
    """
    Use case for fetching and storing stock data from external sources.
    
    This use case is responsible for:
    - Retrieving price data from external providers
    - Validating data integrity
    - Storing data in local repositories
    - Managing data refresh operations
    
    Input Contract:
    - symbol: Valid stock symbol (string)
    - provider: Data provider (DataProvider enum)
    - timeframe: Daily or minute data (boolean)
    - days: Number of days to fetch (integer)
    - force_refresh: Whether to bypass cache (boolean)
    
    Output Contract:
    - List[PricePoint]: Validated price data points
    - dict: Operation results with success/failure counts
    """
    
    def __init__(self, data_service: DataService):
        """
        Initialize the data fetching use case.
        
        Args:
            data_service: Service for data operations
        """
        self._data_service = data_service
        self._logger = logging.getLogger(__name__)
    
    def fetch_daily_prices(
        self, 
        symbol: str, 
        days: int = 365, 
        provider: DataProvider = DataProvider.SSI,
        force_refresh: bool = False,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Fetch daily price data for a symbol.
        
        Args:
            symbol: Stock symbol to fetch
            days: Number of days to retrieve
            provider: Data provider to use
            force_refresh: Whether to bypass local cache
            end_date: End date for data range
            
        Returns:
            List of price points
            
        Raises:
            DataFetchException: If data fetching fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            # Validate inputs
            symbol = validate_symbol(symbol)
            provider = validate_provider(provider)
            days = validate_days_back(days)
            
            self._logger.info(
                "Fetching daily prices: symbol=%s, days=%d, provider=%s",
                symbol, days, provider.value
            )
            
            # Delegate to data service
            prices = self._data_service.get_daily_prices(
                symbol=symbol,
                days=days,
                end_date=end_date,
                force_refresh=force_refresh
            )
            
            self._logger.info(
                "Successfully fetched %d daily prices for %s",
                len(prices), symbol
            )
            return prices
            
        except Exception as e:
            if isinstance(e, (DataFetchException, SymbolNotFoundException)):
                raise
            
            error_msg = f"Failed to fetch daily prices for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)
    
    def fetch_minute_prices(
        self, 
        symbol: str, 
        days: int = 5, 
        provider: DataProvider = DataProvider.SSI,
        force_refresh: bool = False,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Fetch minute price data for a symbol.
        
        Args:
            symbol: Stock symbol to fetch
            days: Number of days to retrieve
            provider: Data provider to use
            force_refresh: Whether to bypass local cache
            end_date: End date for data range
            
        Returns:
            List of price points
            
        Raises:
            DataFetchException: If data fetching fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            # Validate inputs
            symbol = validate_symbol(symbol)
            provider = validate_provider(provider)
            days = validate_days_back(days)
            
            self._logger.info(
                "Fetching minute prices: symbol=%s, days=%d, provider=%s",
                symbol, days, provider.value
            )
            
            # Delegate to data service
            prices = self._data_service.get_minute_prices(
                symbol=symbol,
                days=days,
                end_date=end_date,
                force_refresh=force_refresh
            )
            
            self._logger.info(
                "Successfully fetched %d minute prices for %s",
                len(prices), symbol
            )
            return prices
            
        except Exception as e:
            if isinstance(e, (DataFetchException, SymbolNotFoundException)):
                raise
            
            error_msg = f"Failed to fetch minute prices for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)
    
    def refresh_symbol_data(
        self, 
        symbol: str, 
        provider: DataProvider = DataProvider.SSI
    ) -> dict:
        """
        Refresh all data for a specific symbol.
        
        Args:
            symbol: Stock symbol to refresh
            provider: Data provider to use
            
        Returns:
            Dictionary with operation results
        """
        try:
            symbol = validate_symbol(symbol)
            provider = validate_provider(provider)
            
            self._logger.info(
                "Refreshing data for symbol: %s using provider: %s",
                symbol, provider.value
            )
            
            # Fetch fresh daily data
            daily_prices = self.fetch_daily_prices(
                symbol=symbol,
                provider=provider,
                force_refresh=True
            )
            
            return {
                "success": True,
                "symbol": symbol,
                "daily_count": len(daily_prices),
                "message": f"Successfully refreshed data for {symbol}"
            }
            
        except Exception as e:
            error_msg = f"Failed to refresh data for {symbol}: {str(e)}"
            self._logger.error(error_msg)
            return {
                "success": False,
                "symbol": symbol,
                "error": error_msg,
                "daily_count": 0
            }
    
    def bulk_refresh_symbols(
        self, 
        symbols: List[str], 
        provider: DataProvider = DataProvider.SSI,
        max_workers: int = 10
    ) -> dict:
        """
        Refresh data for multiple symbols in parallel.
        
        Args:
            symbols: List of symbols to refresh
            provider: Data provider to use
            max_workers: Maximum number of parallel workers
            
        Returns:
            Dictionary with bulk operation results
        """
        try:
            provider = validate_provider(provider)
            
            self._logger.info(
                "Starting bulk refresh for %d symbols using %s",
                len(symbols), provider.value
            )
            
            # Delegate to data service for parallel processing
            results = self._data_service.refresh_all_symbols(
                provider=provider,
                max_workers=max_workers
            )
            
            self._logger.info("Bulk refresh completed: %s", results["message"])
            return results
            
        except Exception as e:
            error_msg = f"Failed to execute bulk refresh: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)
    
    def validate_data_source(self, provider: DataProvider) -> bool:
        """
        Validate connection and data availability from a provider.
        
        Args:
            provider: Data provider to validate
            
        Returns:
            True if provider is accessible and returns valid data
        """
        try:
            provider = validate_provider(provider)
            
            self._logger.info("Validating data source: %s", provider.value)
            
            # Test with a known symbol
            test_symbol = "VIC"
            test_prices = self.fetch_daily_prices(
                symbol=test_symbol,
                days=5,
                provider=provider,
                force_refresh=True
            )
            
            is_valid = len(test_prices) > 0
            self._logger.info(
                "Data source validation for %s: %s",
                provider.value, "PASSED" if is_valid else "FAILED"
            )
            return is_valid
            
        except Exception as e:
            self._logger.warning(
                "Data source validation failed for %s: %s",
                provider.value, str(e)
            )
            return False
