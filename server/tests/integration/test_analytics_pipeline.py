"""
Integration tests for the analytics pipeline.

These tests validate ML analytics, backtesting, and advanced analysis features.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from shared.models.stock_models import (
    TechnicalIndicator, SignalType, ConfidenceLevel,
    TrendDirection, MarketCondition
)
from core.analytics.ml_analytics_service import TrendPrediction
from core.analytics.backtesting_service import OrderType


class TestAnalyticsPipeline:
    """Test the complete analytics pipeline integration."""

    def test_ml_trend_prediction_pipeline(self, ml_analytics_service, sample_trending_up_prices):
        """Test ML-based trend prediction pipeline."""
        symbol = "UPTREND"

        # Create mock technical indicators
        indicators = [
            TechnicalIndicator(
                name="RSI",
                value=65.0,
                signal=SignalType.BUY,
                confidence=ConfidenceLevel.MEDIUM,
                description="RSI indicates bullish momentum",
                timestamp=int(datetime.now().timestamp())
            ),
            TechnicalIndicator(
                name="MACD",
                value=2.5,
                signal=SignalType.BUY,
                confidence=ConfidenceLevel.HIGH,
                description="MACD shows positive crossover",
                timestamp=int(datetime.now().timestamp())
            )
        ]

        # Create market condition
        market_condition = MarketCondition(
            condition="trending",
            volatility=0.15,
            volume_trend="increasing"
        )

        # Perform trend prediction
        trend_prediction = ml_analytics_service.predict_trend(
            symbol=symbol,
            prices=sample_trending_up_prices,
            indicators=indicators,
            market_condition=market_condition
        )

        # Validate prediction results
        assert isinstance(trend_prediction, TrendPrediction)
        assert trend_prediction.direction in [TrendDirection.BULLISH, TrendDirection.BEARISH, TrendDirection.SIDEWAYS]
        assert 0 <= trend_prediction.strength <= 1
        assert 0 <= trend_prediction.confidence <= 1
        assert trend_prediction.duration_days > 0
        assert len(trend_prediction.key_levels) > 0
        assert trend_prediction.reasoning is not None

    def test_signal_enhancement_pipeline(self, ml_analytics_service, sample_prices):
        """Test signal enhancement with ML weighting."""
        symbol = "TEST"

        # Create test signals
        signals = [
            TechnicalIndicator(
                name="RSI",
                value=70.0,
                signal=SignalType.SELL,
                confidence=ConfidenceLevel.MEDIUM,
                description="RSI overbought",
                timestamp=int(datetime.now().timestamp())
            ),
            TechnicalIndicator(
                name="MACD",
                value=-1.2,
                signal=SignalType.SELL,
                confidence=ConfidenceLevel.HIGH,
                description="MACD bearish crossover",
                timestamp=int(datetime.now().timestamp())
            ),
            TechnicalIndicator(
                name="MA",
                value=105.0,
                signal=SignalType.BUY,
                confidence=ConfidenceLevel.LOW,
                description="Price above moving average",
                timestamp=int(datetime.now().timestamp())
            )
        ]

        # Create trend prediction context
        trend_prediction = TrendPrediction(
            direction=TrendDirection.BEARISH,
            strength=0.7,
            confidence=0.8,
            duration_days=10,
            key_levels=[95.0, 100.0, 105.0],
            reasoning="Strong downtrend detected"
        )

        # Create market condition
        market_condition = MarketCondition(
            condition="trending",
            volatility=0.20,
            volume_trend="decreasing"
        )

        # Enhance signals
        enhanced_signals = ml_analytics_service.enhance_signals(
            symbol=symbol,
            signals=signals,
            market_condition=market_condition,
            trend_prediction=trend_prediction
        )

        # Validate enhancement results
        assert len(enhanced_signals) == len(signals)
        for enhancement in enhanced_signals:
            assert enhancement.original_signal in [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
            assert enhancement.enhanced_signal in [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
            assert enhancement.weight_adjustment > 0
            assert enhancement.reasoning is not None

    def test_price_prediction_pipeline(self, ml_analytics_service, sample_prices):
        """Test ML-based price prediction pipeline."""
        symbol = "TEST"

        # Create trend prediction context
        trend_prediction = TrendPrediction(
            direction=TrendDirection.BULLISH,
            strength=0.6,
            confidence=0.75,
            duration_days=15,
            key_levels=[98.0, 102.0, 106.0],
            reasoning="Moderate uptrend with good momentum"
        )

        # Predict price target
        price_prediction = ml_analytics_service.predict_price_target(
            symbol=symbol,
            prices=sample_prices,
            trend_prediction=trend_prediction,
            horizon_days=5
        )

        # Validate prediction results
        assert price_prediction.predicted_price > 0
        assert 0 <= price_prediction.confidence <= 1
        assert price_prediction.prediction_horizon_days == 5
        assert price_prediction.model_used is not None
        assert len(price_prediction.features_used) > 0
        assert isinstance(price_prediction.timestamp, datetime)

    def test_risk_metrics_calculation(self, ml_analytics_service, sample_volatile_prices):
        """Test risk metrics calculation pipeline."""
        symbol = "VOLATILE"

        # Calculate risk metrics
        risk_metrics = ml_analytics_service.calculate_risk_metrics(
            symbol=symbol,
            prices=sample_volatile_prices,
            position_size=10000.0
        )

        # Validate risk metrics
        assert "volatility" in risk_metrics
        assert "var_95" in risk_metrics
        assert "var_99" in risk_metrics
        assert "max_drawdown" in risk_metrics
        assert "sharpe_ratio" in risk_metrics
        assert "position_size" in risk_metrics

        assert risk_metrics["volatility"] >= 0
        assert risk_metrics["var_95"] <= 0  # VaR should be negative
        assert risk_metrics["var_99"] <= risk_metrics["var_95"]  # 99% VaR should be worse than 95%
        assert risk_metrics["max_drawdown"] <= 0
        assert risk_metrics["position_size"] == 10000.0

    def test_backtesting_pipeline(self, backtesting_service, sample_trending_up_prices):
        """Test complete backtesting pipeline."""
        symbol = "UPTREND"

        # Create trading signals for backtesting
        signals = []
        base_date = datetime.fromtimestamp(sample_trending_up_prices[0].timestamp)

        # Add some buy and sell signals
        signals.append((base_date + timedelta(days=5), SignalType.BUY, 0.8))
        signals.append((base_date + timedelta(days=15), SignalType.SELL, 0.7))
        signals.append((base_date + timedelta(days=25), SignalType.BUY, 0.9))
        signals.append((base_date + timedelta(days=35), SignalType.SELL, 0.6))

        # Run backtest
        backtest_result = backtesting_service.backtest_strategy(
            symbol=symbol,
            prices=sample_trending_up_prices,
            signals=signals,
            stop_loss_percent=0.05,
            take_profit_percent=0.10
        )

        # Validate backtest results
        assert backtest_result.symbol == symbol
        assert backtest_result.initial_capital == backtesting_service.initial_capital
        assert backtest_result.final_capital > 0
        assert backtest_result.total_trades >= 0
        assert 0 <= backtest_result.win_rate <= 100
        assert len(backtest_result.trades) == backtest_result.total_trades
        assert len(backtest_result.equity_curve) > 0

        # Validate individual trades
        for trade in backtest_result.trades:
            assert trade.symbol == symbol
            assert trade.entry_price > 0
            assert trade.exit_price > 0
            assert trade.quantity > 0
            assert trade.order_type in [OrderType.BUY, OrderType.SELL]
            assert trade.holding_period_days >= 0

    def test_portfolio_analysis_pipeline(self, backtesting_service, sample_prices, sample_trending_up_prices):
        """Test portfolio-level analysis across multiple symbols."""
        # Create backtest results for multiple symbols
        backtest_results = []

        # Test symbol 1
        signals1 = [(datetime.now() - timedelta(days=50), SignalType.BUY, 0.8)]
        result1 = backtesting_service.backtest_strategy(
            symbol="TEST1",
            prices=sample_prices,
            signals=signals1
        )
        backtest_results.append(result1)

        # Test symbol 2
        signals2 = [(datetime.now() - timedelta(days=30), SignalType.BUY, 0.9)]
        result2 = backtesting_service.backtest_strategy(
            symbol="TEST2",
            prices=sample_trending_up_prices,
            signals=signals2
        )
        backtest_results.append(result2)

        # Analyze portfolio performance
        portfolio_analysis = backtesting_service.analyze_strategy_performance(backtest_results)

        # Validate portfolio analysis
        assert "summary" in portfolio_analysis
        assert "performance" in portfolio_analysis
        assert "best_performer" in portfolio_analysis
        assert "worst_performer" in portfolio_analysis

        summary = portfolio_analysis["summary"]
        assert summary["total_symbols"] == 2
        assert summary["total_trades"] >= 0
        assert 0 <= summary["overall_win_rate"] <= 100

        performance = portfolio_analysis["performance"]
        assert "average_return_percent" in performance
        assert "average_sharpe_ratio" in performance
        assert "consistency_score" in performance

    def test_end_to_end_analytics_workflow(self, ml_analytics_service, backtesting_service, sample_trending_up_prices):
        """Test complete end-to-end analytics workflow."""
        symbol = "WORKFLOW_TEST"

        # Step 1: Create technical indicators
        indicators = [
            TechnicalIndicator(
                name="RSI",
                value=55.0,
                signal=SignalType.BUY,
                confidence=ConfidenceLevel.MEDIUM,
                description="RSI bullish",
                timestamp=int(datetime.now().timestamp())
            )
        ]

        # Step 2: ML trend prediction
        trend_prediction = ml_analytics_service.predict_trend(
            symbol=symbol,
            prices=sample_trending_up_prices,
            indicators=indicators,
            market_condition=MarketCondition.TRENDING
        )

        # Step 3: Signal enhancement
        enhanced_signals = ml_analytics_service.enhance_signals(
            symbol=symbol,
            signals=indicators,
            market_condition=MarketCondition.TRENDING,
            trend_prediction=trend_prediction
        )

        # Step 4: Price prediction
        price_prediction = ml_analytics_service.predict_price_target(
            symbol=symbol,
            prices=sample_trending_up_prices,
            trend_prediction=trend_prediction
        )

        # Step 5: Risk assessment
        risk_metrics = ml_analytics_service.calculate_risk_metrics(
            symbol=symbol,
            prices=sample_trending_up_prices
        )

        # Step 6: Generate trading signals for backtesting
        trading_signals = []
        if trend_prediction.direction == TrendDirection.UPTREND:
            base_date = datetime.fromtimestamp(sample_trending_up_prices[10].timestamp)
            trading_signals.append((base_date, SignalType.BUY, trend_prediction.confidence))

        # Step 7: Backtest the strategy
        if trading_signals:
            backtest_result = backtesting_service.backtest_strategy(
                symbol=symbol,
                prices=sample_trending_up_prices,
                signals=trading_signals
            )

            # Validate complete workflow
            assert trend_prediction is not None
            assert len(enhanced_signals) > 0
            assert price_prediction is not None
            assert len(risk_metrics) > 0
            assert backtest_result is not None

            # Verify workflow consistency
            assert trend_prediction.confidence > 0
            assert price_prediction.confidence > 0
            assert risk_metrics["volatility"] >= 0
            assert backtest_result.total_trades >= 0

    def test_analytics_error_handling(self, ml_analytics_service, backtesting_service):
        """Test error handling in analytics pipeline."""
        from shared.exceptions.stock_exceptions import InsufficientDataException, AnalysisException

        # Test with insufficient data
        minimal_prices = [
            # Only 2 price points - insufficient for analysis
        ]

        with pytest.raises(InsufficientDataException):
            ml_analytics_service.predict_trend(
                symbol="ERROR_TEST",
                prices=minimal_prices,
                indicators=[],
                market_condition=MarketCondition.TRENDING
            )

        with pytest.raises(InsufficientDataException):
            backtesting_service.backtest_strategy(
                symbol="ERROR_TEST",
                prices=minimal_prices,
                signals=[]
            )

    def test_analytics_performance_benchmarks(self, ml_analytics_service, sample_prices):
        """Test analytics performance benchmarks."""
        import time

        symbol = "PERF_TEST"

        # Benchmark trend prediction
        start_time = time.time()
        trend_prediction = ml_analytics_service.predict_trend(
            symbol=symbol,
            prices=sample_prices,
            indicators=[],
            market_condition=MarketCondition.TRENDING
        )
        trend_time = time.time() - start_time

        # Benchmark price prediction
        start_time = time.time()
        price_prediction = ml_analytics_service.predict_price_target(
            symbol=symbol,
            prices=sample_prices,
            trend_prediction=trend_prediction
        )
        price_time = time.time() - start_time

        # Benchmark risk calculation
        start_time = time.time()
        risk_metrics = ml_analytics_service.calculate_risk_metrics(
            symbol=symbol,
            prices=sample_prices
        )
        risk_time = time.time() - start_time

        # Validate performance (should complete within reasonable time)
        assert trend_time < 5.0  # Should complete within 5 seconds
        assert price_time < 3.0  # Should complete within 3 seconds
        assert risk_time < 2.0   # Should complete within 2 seconds

        # Validate results are still correct
        assert trend_prediction is not None
        assert price_prediction is not None
        assert len(risk_metrics) > 0
